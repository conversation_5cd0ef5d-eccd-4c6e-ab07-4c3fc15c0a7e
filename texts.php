<?php

// Text configurations for different languages
$texts = [
    'fa' => [
        'welcome_message' => "سلام {first_name} 👋\n\nبه ربات نجوا گرام خوش آمدید.\n\nبا استفاده از این ربات شما می توانید متن و یا سایر محتوا را در چت های خصوصی و گروه نجوا کنید.\n\n> 📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور /help مطالعه کنید.",
        
        'membership_required' => "سلام {first_name} 👋\n\nبرای استفاده از ربات، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
        
        'membership_not_found' => "❌ شما هنوز عضو نیستید!\n\nبرای استفاده از ربات، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
        
        'checking_membership' => "⌛️ در حال بررسی عضویت شما...",
        
        // Main menu buttons
        'btn_najva_section' => '💬 بخش نجوا',
        'btn_privacy' => '👀 حریم خصوصی',
        'btn_help' => '📚 راهنما',
        'btn_support' => '☎️ پشتیبانی',
        'btn_language' => '🇮🇷 زبان',
        
        // Channel buttons
        'btn_join_channel_1' => '🔔 عضویت در کانال اول',
        'btn_join_channel_2' => '🔔 عضویت در کانال دوم',
        'btn_check_membership' => '🔄 بررسی عضویت',
        
        // Channel names
        'channel_1_name' => 'کانال اول',
        'channel_2_name' => 'کانال دوم'
    ],
    
    'en' => [
        'welcome_message' => "Hello {first_name} 👋\n\nWelcome to Najva Gram Bot.\n\nUsing this bot, you can send anonymous messages and content in private chats and groups.\n\n> 📚 Please read the bot usage instructions through the Help button or /help command.",
        
        'membership_required' => "Hello {first_name} 👋\n\nTo use the bot, please first join the channels below and then click the «Check Membership» button:",
        
        'membership_not_found' => "❌ You are not a member yet!\n\nTo use the bot, please first join the channels below and then click the «Check Membership» button:",
        
        'checking_membership' => "⌛️ Checking your membership...",
        
        // Main menu buttons
        'btn_najva_section' => '💬 Anonymous Section',
        'btn_privacy' => '👀 Privacy',
        'btn_help' => '📚 Help',
        'btn_support' => '☎️ Support',
        'btn_language' => '🇺🇸 Language',
        
        // Channel buttons
        'btn_join_channel_1' => '🔔 Join First Channel',
        'btn_join_channel_2' => '🔔 Join Second Channel',
        'btn_check_membership' => '🔄 Check Membership',
        
        // Channel names
        'channel_1_name' => 'First Channel',
        'channel_2_name' => 'Second Channel'
    ]
];

// Default language
$default_language = 'fa';

/**
 * Get text by key for specific language
 * @param string $key Text key
 * @param string $lang Language code (default: fa)
 * @param array $replacements Array of replacements for placeholders
 * @return string
 */
if (!function_exists('getText')) {
    function getText($key, $lang = null, $replacements = []) {
    global $texts, $default_language;
    
    if ($lang === null) {
        $lang = $default_language;
    }
    
    // Fallback to default language if key not found in requested language
    if (!isset($texts[$lang][$key])) {
        $lang = $default_language;
    }
    
    // Return key if not found in any language
    if (!isset($texts[$lang][$key])) {
        return $key;
    }
    
    $text = $texts[$lang][$key];
    
    // Replace placeholders
    foreach ($replacements as $placeholder => $value) {
        $text = str_replace('{' . $placeholder . '}', $value, $text);
    }
    
    return $text;
    }
}

/**
 * Create main menu keyboard
 * @param string $lang Language code
 * @return string JSON encoded keyboard
 */
if (!function_exists('createMainMenuKeyboard')) {
    function createMainMenuKeyboard($lang = null) {
    return json_encode([
        'inline_keyboard' => [
            [
                ['text' => getText('btn_najva_section', $lang), 'callback_data' => 'najva_section'],
                ['text' => getText('btn_privacy', $lang), 'callback_data' => 'privacy']
            ],
            [
                ['text' => getText('btn_help', $lang), 'callback_data' => 'help'],
                ['text' => getText('btn_support', $lang), 'callback_data' => 'support']
            ],
            [
                ['text' => getText('btn_language', $lang), 'callback_data' => 'language']
            ]
        ]
    ]);
    }
}

?>
